{"accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "cmn-Hans-CN"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 139}, "autofill": {"last_version_deduped": 139}, "bookmark": {"storage_computation_last_update": "1339836**********"}, "browser": {"window_placement": {"bottom": 768, "left": 0, "maximized": false, "right": 1366, "top": 0, "work_area_bottom": 600, "work_area_left": 0, "work_area_right": 800, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "data_sharing": {"eligible_for_version_out_of_date_instant_message": false, "eligible_for_version_out_of_date_persistent_message": false, "has_shown_any_version_out_of_date_message": false}, "default_search_provider": {"guid": ""}, "dips_timer_last_update": "*****************", "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "9aa0ba0f-076d-4860-834b-16330f8fafaf", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "139.0.7258.5"}, "gaia_cookie": {"changed_time": **********.298713, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_binary_data": "", "periodic_report_time_2": "*****************"}, "gcm": {"product_category_for_subtypes": "org.chromium.windows"}, "google": {"services": {"consented_to_sync": false, "signin_scoped_device_id": "e65bb328-28c5-4b8a-b742-132444fb1be8"}}, "https_upgrade_navigations": {"2025-07-31": 20}, "intl": {"selected_languages": "zh-CN,zh"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"engagement": {"schema_version": 5}}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"predictionmodelfetcher": {"last_fetch_attempt": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_TRACKING": true, "SAVED_TAB_GROUP": true, "SHOPPING_PAGE_PREDICTOR": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"anti_abuse_initialized": true, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "accessibility_events": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]xuangutong.com.cn,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://xuangutong.com.cn:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 56}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://xuangutong.com.cn:443,*": {"last_modified": "13398400505735364", "setting": {"lastEngagementTime": 1.339840050573535e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 28.58663522304}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "120.0.6099.28", "creation_time": "13398355755707404", "exit_type": "Crashed", "family_member_role": "not_in_family", "isolated_web_app": {"install": {"pending_initialization_count": 0}}, "last_engagement_time": "13398400505735350", "last_time_obsolete_http_credentials_removed": 1753886421.696521, "last_time_password_store_metrics_reported": 1753886271.610068, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "用户1", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13398355755", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQyLfLldW35hcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEPm3y5XVt+YX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13398307199000000", "uma_in_sql_start_time": "13398362297753109"}, "sessions": {"event_log": [{"crashed": false, "time": "13398359961711810", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398360050453524", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398362297751542", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398362298485653", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398362315518177", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398362328522360", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398362367451757", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398362375902647", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398362433422310", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398362513534261", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398379640534838", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398379679327046", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398380015265878", "type": 0}, {"crashed": true, "time": "13398380234488176", "type": 0}, {"crashed": true, "time": "13398380625519260", "type": 0}, {"crashed": true, "time": "13398380846940911", "type": 0}, {"crashed": true, "time": "13398397716536960", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 3, "time": "13398397815025788", "type": 2, "window_count": 1}, {"crashed": true, "time": "13398398947368653", "type": 0}], "session_data_status": 1}, "settings": {"a11y": {"apply_page_colors_only_on_increased_contrast": true}, "force_google_safesearch": false}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "site_search_settings": {"overridden_keywords": []}, "spellcheck": {"dictionaries": ["zh-CN", "en-US"], "dictionary": ""}, "supervised_user": {"metrics": {"day_id": 155072}}, "sync": {"autofill_wallet_import_enabled_migrated": true, "data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "segmentation": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "tracking_protection": {"tracking_protection_3pcd_enabled": false}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "error_loaded_policy_apps_migrated": true, "last_preinstall_synchronize_version": "139"}}